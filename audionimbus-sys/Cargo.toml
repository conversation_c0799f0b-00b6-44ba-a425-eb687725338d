[package]
name = "audionimbus-sys"
description = "Rust bindings for Steam Audio."
version = "4.6.2-fmodwwise.2"
edition = "2021"
authors = ["<PERSON><PERSON> Mai<PERSON> <<EMAIL>>"]
license = "MIT OR Apache-2.0"
keywords = ["gamedev", "game", "audio", "engine", "steam"]
categories = ["game-development", "game-engines", "multimedia::audio", "simulation", "external-ffi-bindings"]
repository = "https://github.com/MaxenceMaire/audionimbus"
readme = "README.md"
exclude = [
  "steam-audio/**",
  "!steam-audio/core/src/core/**/*.h",
  "!steam-audio/fmod/src/**/*.h",
  "!steam-audio/fmod/include/**",
  "!steam-audio/wwise/src/**/*.h",
  "!steam-audio/wwise/include/**"
]

[build-dependencies]
bindgen = "0.71.1"

[features]
fmod = []
wwise = []

[package.metadata.docs.rs]
features = ["fmod"]
rustdoc-args = ["--cfg", "docsrs"]
