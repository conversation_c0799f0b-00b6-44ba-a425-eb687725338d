{"FileVersion": 3, "Version": 1, "VersionName": "4.6.1", "FriendlyName": "Steam Audio FMOD Studio Support", "Description": "Integrates the Steam Audio plugin for Unreal and the Steam Audio plugin for FMOD Studio.", "Category": "Audio", "CreatedBy": "Valve Corporation", "CreatedByURL": "https://valvesoftware.github.io/steam-audio", "DocsURL": "https://valvesoftware.github.io/steam-audio/doc/unreal/index.html", "MarketplaceURL": "", "SupportURL": "https://steamcommunity.com/app/596420", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "SteamAudioFMODStudio", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault", "WhitelistPlatforms": ["Win64", "Linux", "<PERSON>", "Android", "IOS"]}], "Plugins": [{"Name": "FMODStudio", "Enabled": true}, {"Name": "SteamAudio", "Enabled": true}]}