//
// Copyright 2017-2023 Valve Corporation.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#pragma once

#include "CoreMinimal.h"
#include "SteamAudioBaking.h"


namespace SteamAudio {

// ---------------------------------------------------------------------------------------------------------------------
// FBakeWindow
// ---------------------------------------------------------------------------------------------------------------------

struct FBakeWindowRow
{
    EBakeTaskType Type;
    AActor* Actor;
    int Size;
};

class FBakeWindow : public TSharedFromThis<FBakeWindow>
{
public:
    FBakeWindow();
    ~FBakeWindow();

    void Invoke();

private:
    TArray<TSharedPtr<FBakeWindowRow>> BakeWindowRows;
    TSharedPtr<SListView<TSharedPtr<FBakeWindowRow>>> ListView;

    TSharedRef<SDockTab> SpawnTab(const FSpawnTabArgs& SpawnTabArgs);
    TSharedRef<ITableRow> OnGenerateRow(TSharedPtr<FBakeWindowRow> Item, const TSharedRef<STableViewBase>& OwnerTable);
    bool IsBakeEnabled() const;
    FReply OnBakeSelected();
    void OnBakeComplete();
    void RefreshBakeTasks();
};

}
