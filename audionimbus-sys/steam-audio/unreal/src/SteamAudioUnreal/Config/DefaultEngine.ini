

[/Script/EngineSettings.GameMapsSettings]
EditorStartupMap=/Game/Test/Maps/TestFMOD.TestFMOD
LocalMapOptions=
TransitionMap=None
bUseSplitscreen=True
TwoPlayerSplitscreenLayout=Horizontal
ThreePlayerSplitscreenLayout=FavorTop
FourPlayerSplitscreenLayout=Grid
bOffsetPlayerGamepadIds=False
GameInstanceClass=/Script/Engine.GameInstance
GameDefaultMap=/Game/Test/Maps/OcclusionDynamic.OcclusionDynamic
ServerDefaultMap=/Engine/Maps/Entry.Entry
GlobalDefaultGameMode=/Script/SteamAudioUnreal.SteamAudioUnrealGameModeBase
GlobalDefaultServerGameMode=None

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_Blank",NewGameName="/Script/SteamAudioUnreal")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_Blank",NewGameName="/Script/SteamAudioUnreal")
+ActiveClassRedirects=(OldClassName="TP_BlankGameModeBase",NewClassName="SteamAudioUnrealGameModeBase")

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
SpatializationPlugin=Steam Audio Spatialization
OcclusionPlugin=Steam Audio Occlusion
ReverbPlugin=Steam Audio Reverb

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
SpatializationPlugin=Steam Audio Spatialization

[/Script/SteamAudio.SteamAudioSettings]
DefaultMeshMaterial=/SteamAudio/Materials/Default.Default
DefaultLandscapeMaterial=/SteamAudio/Materials/Default.Default
DefaultBSPMaterial=/SteamAudio/Materials/Default.Default
ReverbSubmix=/Game/Test/Submixes/ReverbSubmix.ReverbSubmix
SceneType=DEFAULT

[/Script/AndroidRuntimeSettings.AndroidRuntimeSettings]
bBuildForArm64=True
bBuildForArmV7=True

[/Script/FMODStudio.FMODSettings]
bLoadAllBanks=True
bLoadAllSampleData=False
bEnableLiveUpdate=True
bEnableEditorLiveUpdate=False
BankOutputDirectory=(Path="FMOD")
OutputFormat=Stereo
bVol0Virtual=True
Vol0VirtualLevel=0.000100
SampleRate=0
bMatchHardwareSampleRate=True
RealChannelCount=64
TotalChannelCount=512
DSPBufferLength=0
DSPBufferCount=0
FileBufferSize=2048
StudioUpdatePeriod=0
InitialOutputDriverName=
bLockAllBuses=False
MemoryPoolSizes=(Desktop=0,Mobile=0,PS4=0,Switch=0,XboxOne=0)
LiveUpdatePort=9264
EditorLiveUpdatePort=9265
ReloadBanksDelay=5
bEnableMemoryTracking=False
+PluginFiles=phonon_fmod
ContentBrowserPrefix=/Game/FMOD/
ForcePlatformName=
MasterBankName=Master
SkipLoadBankName=
StudioBankKey=
WavWriterPath=
LoggingLevel=LEVEL_NONE
OcclusionParameter=
AmbientVolumeParameter=
AmbientLPFParameter=

