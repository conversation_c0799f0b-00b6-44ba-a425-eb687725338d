

[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=07DAA4C14DFC68F5DB1392A6E25A068A

[/Script/AkAudio.AkWindowsInitializationSettings]
CommonSettings=(SampleRate=48000,MaximumNumberOfMemoryPools=256,MaximumNumberOfPositioningPaths=255,CommandQueueSize=262144,SamplesPerFrame=512,MainOutputSettings=(AudioDeviceShareSet="",DeviceID=0,PanningRule=Speakers,ChannelConfigType=Anonymous,ChannelMask=0,NumberOfChannels=0),StreamingLookAheadRatio=1.000000,NumberOfRefillsInVoice=4,SpatialAudioSettings=(MaxSoundPropagationDepth=8,MovementThreshold=25.000000,NumberOfPrimaryRays=35,ReflectionOrder=2,DiffractionOrder=4,MaxEmitterRoomAuxSends=3,DiffractionOnReflectionsOrder=2,MaximumPathLength=100000.000000,CPULimitPercentage=0.000000,LoadBalancingSpread=1,EnableGeometricDiffractionAndTransmission=True,CalcEmitterVirtualPosition=True))
CommunicationSettings=(InitializeSystemComms=True,PoolSize=262144,DiscoveryBroadcastPort=24024,CommandPort=0,NetworkName="")
AdvancedSettings=(UseHeadMountedDisplayAudioDevice=False,MaxSystemAudioObjects=128,EnableMultiCoreRendering=False,MaxNumJobWorkers=1,JobWorkerMaxExecutionTimeUSec=0,IO_MemorySize=2097152,IO_Granularity=32768,TargetAutoStreamBufferLength=380.000000,UseStreamCache=False,MaximumPinnedBytesInCache=**********,EnableGameSyncPreparation=False,ContinuousPlaybackLookAhead=1,MonitorQueuePoolSize=1048576,MaximumHardwareTimeoutMs=1000,DebugOutOfRangeCheckEnabled=False,DebugOutOfRangeLimit=16.000000,VmPageSize=65536)

[/Script/AkAudio.AkSettings]
MaxSimultaneousReverbVolumes=4
WwiseProjectPath=(FilePath="")
RootOutputPath=(Path="")
WwiseStagingDirectory=(Path="WwiseAudio")
bSoundBanksTransfered=True
bAssetsMigrated=True
bProjectMigrated=True
DefaultOcclusionCollisionChannel=ECC_Visibility
DefaultFitToGeometryCollisionChannel=ECC_WorldStatic
AkGeometryMap=()
DefaultAcousticTexture=None
DefaultTransmissionLoss=0.000000
GeometrySurfacePropertiesTable=/Game/WwiseAudio/DefaultGeometrySurfacePropertiesTable.DefaultGeometrySurfacePropertiesTable
GlobalDecayAbsorption=0.500000
DefaultReverbAuxBus=None
EnvironmentDecayAuxBusMap=()
ReverbAssignmentTable=/Game/WwiseAudio/DefaultReverbAssignmentTable.DefaultReverbAssignmentTable
HFDampingName=
DecayEstimateName=
TimeToFirstReflectionName=
HFDampingRTPC=None
DecayEstimateRTPC=None
TimeToFirstReflectionRTPC=None
AudioInputEvent=None
SplitSwitchContainerMedia=False
SplitMediaPerFolder=False
UseEventBasedPackaging=False
UnrealCultureToWwiseCulture=()
DefaultAssetCreationPath=/Game/WwiseAudio
InitBank=None
AudioRouting=Custom
bWwiseSoundEngineEnabled=True
bWwiseAudioLinkEnabled=False
bAkAudioMixerEnabled=False
DefaultScalingFactor=1.000000
MigratedEnableMultiCoreRendering=True
FixupRedirectorsDuringMigration=False

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Development
BuildTarget=
FullRebuild=False
ForDistribution=False
IncludeDebugFiles=False
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=True
bUseZenStore=False
bMakeBinaryConfig=False
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
WriteBackMetadataToAssetRegistry=Disabled
bWritePluginSizeSummaryJsons=False
bCompressed=True
PackageCompressionFormat=Oodle
bForceUseProjectCompressionFormatIgnoreHardwareOverride=False
PackageAdditionalCompressionOptions=
PackageCompressionMethod=Kraken
PackageCompressionLevel_DebugDevelopment=4
PackageCompressionLevel_TestShipping=4
PackageCompressionLevel_Distribution=7
PackageCompressionMinBytesSaved=1024
PackageCompressionMinPercentSaved=5
bPackageCompressionEnableDDC=False
PackageCompressionMinSizeToConsiderDDC=0
HttpChunkInstallDataVersion=
IncludePrerequisites=True
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bDeterministicShaderCodeOrder=False
bSharedMaterialNativeLibraries=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
LocalizationTargetCatchAllChunkId=0
bCookAll=False
bCookMapsOnly=False
bSkipEditorContent=False
bSkipMovies=False
-IniKeyDenylist=KeyStorePassword
-IniKeyDenylist=KeyPassword
-IniKeyDenylist=rsa.privateexp
-IniKeyDenylist=rsa.modulus
-IniKeyDenylist=rsa.publicexp
-IniKeyDenylist=aes.key
-IniKeyDenylist=SigningPublicExponent
-IniKeyDenylist=SigningModulus
-IniKeyDenylist=SigningPrivateExponent
-IniKeyDenylist=EncryptionKey
-IniKeyDenylist=DevCenterUsername
-IniKeyDenylist=DevCenterPassword
-IniKeyDenylist=IOSTeamID
-IniKeyDenylist=SigningCertificate
-IniKeyDenylist=MobileProvision
-IniKeyDenylist=IniKeyDenylist
-IniKeyDenylist=IniSectionDenylist
+IniKeyDenylist=KeyStorePassword
+IniKeyDenylist=KeyPassword
+IniKeyDenylist=rsa.privateexp
+IniKeyDenylist=rsa.modulus
+IniKeyDenylist=rsa.publicexp
+IniKeyDenylist=aes.key
+IniKeyDenylist=SigningPublicExponent
+IniKeyDenylist=SigningModulus
+IniKeyDenylist=SigningPrivateExponent
+IniKeyDenylist=EncryptionKey
+IniKeyDenylist=DevCenterUsername
+IniKeyDenylist=DevCenterPassword
+IniKeyDenylist=IOSTeamID
+IniKeyDenylist=SigningCertificate
+IniKeyDenylist=MobileProvision
+IniKeyDenylist=IniKeyDenylist
+IniKeyDenylist=IniSectionDenylist
-IniSectionDenylist=HordeStorageServers
-IniSectionDenylist=StorageServers
+IniSectionDenylist=HordeStorageServers
+IniSectionDenylist=StorageServers
+DirectoriesToAlwaysCook=(Path="/Wwise/WwiseTree")
+DirectoriesToAlwaysCook=(Path="/Wwise/WwiseTypes")
bRetainStagedDirectory=False
CustomStageCopyHandler=

