<?xml version="1.0" encoding="utf-8"?>
<!--
The content of this file includes portions of the AUDIOKINETIC Wwise Technology
released in source code form as part of the SDK installer package.

Commercial License Usage

Licensees holding valid commercial licenses to the AUDIOKINETIC Wwise Technology
may use this file in accordance with the end user license agreement provided
with the software or, alternatively, in accordance with the terms contained in a
written agreement between you and Audiokinetic Inc.

Apache License Usage

Alternatively, this file may be used under the Apache License, Version 2.0 (the
"Apache License"); you may not use this file except in compliance with the
Apache License. You may obtain a copy of the Apache License at
http://www.apache.org/licenses/LICENSE-2.0.

Unless required by applicable law or agreed to in writing, software distributed
under the Apache License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES
OR CONDITIONS OF ANY KIND, either express or implied. See the Apache License for
the specific language governing permissions and limitations under the License.

  Copyright (c) 2023 Audiokinetic Inc.
-->
<!--
See https://www.audiokinetic.com/library/edge/?source=SDK&id=plugin__xml.html
for the documentation about the plugin xml format
-->
<PluginModule>
	<EffectPlugin Name="Steam Audio Spatializer" CompanyID="315" PluginID="30323">
		<PluginInfo>
			<PlatformSupport>
				<Platform Name="Any">
					<CanBeInsertOnBusses>true</CanBeInsertOnBusses>
					<CanBeInsertOnAudioObjects>true</CanBeInsertOnAudioObjects>
					<CanBeRendered>true</CanBeRendered>
				</Platform>
			</PlatformSupport>
		</PluginInfo>
		<Properties>
			<Property Name="DirectBinaural" Type="bool" DisplayGroup="Direct Sound" DisplayName="Apply HRTF">
				<AudioEnginePropertyID>7</AudioEnginePropertyID>
				<DefaultValue>true</DefaultValue>
			</Property>
			
			<Property Name="PositionX" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Direct Sound/Position" DisplayName="Position X">
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="10" />
				<DefaultValue>0.0</DefaultValue>
				<AudioEnginePropertyID>8</AudioEnginePropertyID>
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>-1</Min>
							<Max>1</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
			</Property>
			
			<Property Name="PositionY" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Direct Sound/Position" DisplayName="Position Y">
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="10" />
				<DefaultValue>0.0</DefaultValue>
				<AudioEnginePropertyID>9</AudioEnginePropertyID>
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>-1</Min>
							<Max>1</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
			</Property>
			
			<Property Name="PositionZ" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Direct Sound/Position" DisplayName="Position Z">
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="10" />
				<DefaultValue>0.0</DefaultValue>
				<AudioEnginePropertyID>10</AudioEnginePropertyID>
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>-1</Min>
							<Max>1</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
			</Property>

			<Property Name="HRTFInterpolation" Type="int16" DisplayGroup="Direct Sound" DisplayName="HRTF Interpolation">
				<UserInterface DropDown="Menu" />
				<DefaultValue>0</DefaultValue>
				<AudioEnginePropertyID>11</AudioEnginePropertyID>
				<Restrictions>
					<ValueRestriction>
						<Enumeration Type="int16">
							<Value DisplayName="Nearest-Neighbor">0</Value>
							<Value DisplayName="Bilinear">1</Value>
						</Enumeration>
					</ValueRestriction>
				</Restrictions>
			</Property>

			<Property Name="DistanceAttenuation" Type="bool" DisplayGroup="Direct Sound" DisplayName="Distance Attenuation">
				<AudioEnginePropertyID>12</AudioEnginePropertyID>
				<DefaultValue>false</DefaultValue>
			</Property>

			<Property Name="AirAbsorption" Type="bool" DisplayGroup="Direct Sound" DisplayName="Air Absorption">
				<AudioEnginePropertyID>13</AudioEnginePropertyID>
				<DefaultValue>false</DefaultValue>
			</Property>

			<Property Name="Directivity" Type="bool" DisplayGroup="Direct Sound" DisplayName="Directivity (Dipole)">
				<AudioEnginePropertyID>14</AudioEnginePropertyID>
				<DefaultValue>false</DefaultValue>
			</Property>

			<Property Name="DipoleWeight" Type="Real32" DisplayGroup="Direct Sound/Directivity" DisplayName="Dipole Weight">
				<AudioEnginePropertyID>15</AudioEnginePropertyID>
				<DefaultValue>0.0</DefaultValue>
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="10" />
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>0</Min>
							<Max>1</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
				<Dependencies>
					<PropertyDependency Name="Directivity" Action="Enable">
						<Condition>
							<Enumeration Type="bool">
								<Value>true</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
				</Dependencies>
			</Property>

			<Property Name="DipolePower" Type="Real32" DisplayGroup="Direct Sound/Directivity" DisplayName="Dipole Power">
				<AudioEnginePropertyID>16</AudioEnginePropertyID>
				<DefaultValue>0.0</DefaultValue>
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="10" />
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>0</Min>
							<Max>4</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
				<Dependencies>
					<PropertyDependency Name="Directivity" Action="Enable">
						<Condition>
							<Enumeration Type="bool">
								<Value>true</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
				</Dependencies>
			</Property>

			<Property Name="Occlusion" DisplayName="Occlusion" Type="int16" DisplayGroup="Direct Sound/Occlusion">
				<AudioEnginePropertyID>0</AudioEnginePropertyID>
				<DefaultValue>0</DefaultValue>
				<Restrictions>
					<ValueRestriction>
						<Enumeration Type="int16">
							<Value DisplayName="Off">0</Value>
							<Value DisplayName="User-Defined">1</Value>
							<Value DisplayName="Simulation-Defined">2</Value>
						</Enumeration>
					</ValueRestriction>
				</Restrictions>
			</Property>

			<Property Name="OcclusionValue" DisplayName="Occlusion Value" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Direct Sound/Occlusion">
				<AudioEnginePropertyID>1</AudioEnginePropertyID>
				<DefaultValue>1.0</DefaultValue>
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="1" />
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>0</Min>
							<Max>1</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
				<Dependencies>
					<PropertyDependency Name="Occlusion" Action="Enable">
						<Condition>
							<Enumeration Type="int16">
								<Value>1</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
				</Dependencies>
			</Property>

			<Property Name="Transmission" DisplayName="Transmission" Type="int16" DisplayGroup="Direct Sound/Occlusion/Transmission">
				<AudioEnginePropertyID>2</AudioEnginePropertyID>
				<DefaultValue>0</DefaultValue>
				<Restrictions>
					<ValueRestriction>
						<Enumeration Type="int16">
							<Value DisplayName="Off">0</Value>
							<Value DisplayName="User-Defined">1</Value>
							<Value DisplayName="Simulation-Defined">2</Value>
						</Enumeration>
					</ValueRestriction>
				</Restrictions>
				<Dependencies>
					<PropertyDependency Name="Occlusion" Action="Enable">
						<Condition>
							<Enumeration Type="int16">
								<Value>1</Value>
								<Value>2</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
				</Dependencies>
			</Property>

			<Property Name="TransmissionType" DisplayName="Transmission Type" Type="int16" DisplayGroup="Direct Sound/Occlusion/Transmission">
				<AudioEnginePropertyID>3</AudioEnginePropertyID>
				<DefaultValue>0</DefaultValue>
				<Restrictions>
					<ValueRestriction>
						<Enumeration Type="int16">
							<Value DisplayName="Frequency-Independent">0</Value>
							<Value DisplayName="Frequency-Dependent">1</Value>
						</Enumeration>
					</ValueRestriction>
				</Restrictions>
				<Dependencies>
					<PropertyDependency Name="Transmission" Action="Enable">
						<Condition>
							<Enumeration Type="int16">
								<Value>1</Value>
								<Value>2</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
				</Dependencies>
			</Property>

			<Property Name="TransmissionLow" DisplayName="Transmission (Low Frequency)" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Direct Sound/Occlusion/Transmission">
				<AudioEnginePropertyID>4</AudioEnginePropertyID>
				<DefaultValue>1.0</DefaultValue>
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="1" />
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>0</Min>
							<Max>1</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
				<Dependencies>
					<PropertyDependency Name="Transmission" Action="Enable">
						<Condition>
							<Enumeration Type="int16">
								<Value>1</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
					<PropertyDependency Name="TransmissionType" Action="Enable">
						<Condition>
							<Enumeration Type="int16">
								<Value>1</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
				</Dependencies>
			</Property>

			<Property Name="TransmissionMid" DisplayName="Transmission (Mid Frequency)" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Direct Sound/Occlusion/Transmission">
				<AudioEnginePropertyID>5</AudioEnginePropertyID>
				<DefaultValue>1.0</DefaultValue>
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="1" />
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>0</Min>
							<Max>1</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
				<Dependencies>
					<PropertyDependency Name="Transmission" Action="Enable">
						<Condition>
							<Enumeration Type="int16">
								<Value>1</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
				</Dependencies>
			</Property>

			<Property Name="TransmissionHigh" DisplayName="Transmission (High Frequency)" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Direct Sound/Occlusion/Transmission">
				<AudioEnginePropertyID>6</AudioEnginePropertyID>
				<DefaultValue>1.0</DefaultValue>
				<UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="1" />
				<Restrictions>
					<ValueRestriction>
						<Range Type="Real32">
							<Min>0</Min>
							<Max>1</Max>
						</Range>
					</ValueRestriction>
				</Restrictions>
				<Dependencies>
					<PropertyDependency Name="Transmission" Action="Enable">
						<Condition>
							<Enumeration Type="int16">
								<Value>1</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
					<PropertyDependency Name="TransmissionType" Action="Enable">
						<Condition>
							<Enumeration Type="int16">
								<Value>1</Value>
							</Enumeration>
						</Condition>
					</PropertyDependency>
				</Dependencies>
			</Property>

      <Property Name="DirectMixLevel" DisplayName="Direct Mix Level" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Direct Sound">
        <AudioEnginePropertyID>17</AudioEnginePropertyID>
        <DefaultValue>1.0</DefaultValue>
        <UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="1" />
        <Restrictions>
          <ValueRestriction>
            <Range Type="Real32">
              <Min>0</Min>
              <Max>1</Max>
            </Range>
          </ValueRestriction>
        </Restrictions>
      </Property>

      <Property Name="Reflections" Type="bool" DisplayGroup="Reflections" DisplayName="Reflections">
        <AudioEnginePropertyID>18</AudioEnginePropertyID>
        <DefaultValue>false</DefaultValue>
      </Property>

      <Property Name="ReflectionsBinaural" Type="bool" DisplayGroup="Reflections" DisplayName="Apply HRTF To Reflections">
        <AudioEnginePropertyID>19</AudioEnginePropertyID>
        <DefaultValue>false</DefaultValue>
        <Dependencies>
          <PropertyDependency Name="Reflections" Action="Enable">
            <Condition>
              <Enumeration Type="bool">
                <Value>true</Value>
              </Enumeration>
            </Condition>
          </PropertyDependency>
        </Dependencies>
      </Property>

      <Property Name="ReflectionsMixLevel" DisplayName="Reflections Mix Level" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Reflections">
        <AudioEnginePropertyID>20</AudioEnginePropertyID>
        <DefaultValue>1.0</DefaultValue>
        <UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="1" />
        <Restrictions>
          <ValueRestriction>
            <Range Type="Real32">
              <Min>0</Min>
              <Max>1</Max>
            </Range>
          </ValueRestriction>
        </Restrictions>
        <Dependencies>
          <PropertyDependency Name="Reflections" Action="Enable">
            <Condition>
              <Enumeration Type="bool">
                <Value>true</Value>
              </Enumeration>
            </Condition>
          </PropertyDependency>
        </Dependencies>
      </Property>

      <Property Name="Pathing" Type="bool" DisplayGroup="Pathing" DisplayName="Pathing">
        <AudioEnginePropertyID>21</AudioEnginePropertyID>
        <DefaultValue>false</DefaultValue>
      </Property>

      <Property Name="PathingBinaural" Type="bool" DisplayGroup="Pathing" DisplayName="Apply HRTF To Pathing">
        <AudioEnginePropertyID>22</AudioEnginePropertyID>
        <DefaultValue>false</DefaultValue>
        <Dependencies>
          <PropertyDependency Name="Pathing" Action="Enable">
            <Condition>
              <Enumeration Type="bool">
                <Value>true</Value>
              </Enumeration>
            </Condition>
          </PropertyDependency>
        </Dependencies>
      </Property>

      <Property Name="PathingMixLevel" DisplayName="Pathing Mix Level" Type="Real32" SupportRTPCType="Exclusive" DisplayGroup="Pathing">
        <AudioEnginePropertyID>23</AudioEnginePropertyID>
        <DefaultValue>1.0</DefaultValue>
        <UserInterface Step="0.1" Fine="0.001" Decimals="3" UIMax="1" />
        <Restrictions>
          <ValueRestriction>
            <Range Type="Real32">
              <Min>0</Min>
              <Max>1</Max>
            </Range>
          </ValueRestriction>
        </Restrictions>
        <Dependencies>
          <PropertyDependency Name="Pathing" Action="Enable">
            <Condition>
              <Enumeration Type="bool">
                <Value>true</Value>
              </Enumeration>
            </Condition>
          </PropertyDependency>
        </Dependencies>
      </Property>
    </Properties>
	</EffectPlugin>

  <EffectPlugin Name="Steam Audio Mix Return" CompanyID="315" PluginID="30324">
    <PluginInfo>
      <PlatformSupport>
        <Platform Name="Any">
          <CanBeInsertOnBusses>true</CanBeInsertOnBusses>
          <CanBeInsertOnAudioObjects>false</CanBeInsertOnAudioObjects>
          <CanBeRendered>false</CanBeRendered>
        </Platform>
      </PlatformSupport>
    </PluginInfo>
    <Properties>
      <Property Name="Binaural" Type="bool" DisplayName="Apply HRTF">
        <AudioEnginePropertyID>0</AudioEnginePropertyID>
        <DefaultValue>false</DefaultValue>
      </Property>
    </Properties>
  </EffectPlugin>

  <EffectPlugin Name="Steam Audio Reverb" CompanyID="315" PluginID="30325">
    <PluginInfo>
      <PlatformSupport>
        <Platform Name="Any">
          <CanBeInsertOnBusses>true</CanBeInsertOnBusses>
          <CanBeInsertOnAudioObjects>false</CanBeInsertOnAudioObjects>
          <CanBeRendered>false</CanBeRendered>
        </Platform>
      </PlatformSupport>
    </PluginInfo>
    <Properties>
      <Property Name="Binaural" Type="bool" DisplayName="Apply HRTF">
        <AudioEnginePropertyID>0</AudioEnginePropertyID>
        <DefaultValue>false</DefaultValue>
      </Property>
    </Properties>
  </EffectPlugin>
</PluginModule>
