Build Instructions
==================

Overview
--------

Building the Steam Audio Wwise integration involves the following steps:

1.  Building the audio engine plugin (``SteamAudioWwise.dll`` on Windows, for example). This step involves compiling C++ code, and must be done separately on each supported platform.

2.  *(Optional)* Generating the zip file (``steamaudio_wwise.zip``) for distribution.

Directory Structure
^^^^^^^^^^^^^^^^^^^

Below is an overview of the directories contained within the source repository for the Steam Audio Wwise plugin:

================    ==========================================================================================
Directory           Contents
================    ==========================================================================================
``bin/``            Compiled binaries placed by the ``INSTALL`` target of the build system generated by CMake.
``build/``          Build scripts and the build system generated by CMake.
``dist/``           The zip file generated by the ``PACKAGE`` target.
``doc/``            Documentation.
``include/``        Header files for libraries that the Wwise integration depends on.
``lib/``            Binary library files that the Wwise integration depends on.
``src/``            Source code.
================    ==========================================================================================


Building the audio engine plugin
--------------------------------

Requirements
^^^^^^^^^^^^

To build the plugin on any platform, you need the following software:

-   Python (https://www.python.org)
-   CMake 3.17 or later (https://cmake.org)

To build the Windows plugin, you will need:

-   Visual Studio 2019 or later (https://visualstudio.microsoft.com)

To build the Linux plugin, you will need:

-   ``clang`` 11.0 or later (install using your distribution's package manager)
-   ``glibc`` 2.19 or later

To build the macOS plugin, you will need:

-   Xcode 14.0 or later (install from the Mac App Store)

To build the Android plugin, you will need:

-   Android SDK for platform 25 (Android 7.1 Nougat) or later (https://developer.android.com/studio)
-   Android NDK (install using the Android SDK Manager)

To build the iOS plugin, you will need:

-   Xcode 14.0 or later (install from the Mac App Store)

To build the documentation, you will need:

-   Sphinx (https://www.sphinx-doc.org)
-   Sphinx Read The Docs theme (https://sphinx-rtd-theme.readthedocs.io)
-   Sphinx Tabs extension (https://sphinx-tabs.readthedocs.io)
-   Doxygen 1.9 or later (https://www.doxygen.nl)
-   Breathe (https://breathe.readthedocs.io)

Building using the build script
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

To build the plugin, run the following from a command prompt in the ``wwise`` directory of the repository::

    $ cd build
    $ python build.py

On Windows, this will generate a Visual Studio 2019 solution for 64-bit Windows (``build/windows-vs2019-x64/SteamAudioWwise.sln``) and build it in the Release configuration.

On Linux, this will generate a Makefile for 64-bit Linux (``build/linux-x64-release/Makefile``) and build it.

On macOS, this will generate an Xcode project (``build/osx/SteamAudioWwise.xcodeproj``) and build it in the Release configuration.

On Android, this will generate a Makefile for 32-bit ARM (``build/android-armv7-release/Makefile``) and build it.

On iOS, this will generate an Xcode project (``build/ios/SteamAudioWwise.xcodeproj``) and build it in the Release configuration.

If CMake cannot generate the build system due to missing dependencies, you can run CMake directly and adjust settings that control where CMake looks for dependencies, and which targets it builds.

The Steam Audio build script (``build.py``) lets you specify the following command line parameters to control its behavior:

    -p, --platform NAME
        Specifies the target platform. Valid options are ``windows``, ``linux``, ``osx``, ``android``, and ``ios``. Default is the current host platform.

    -t, --toolchain NAME
        Specifies the compiler version (on Windows only). Valid options are ``vs2019`` and ``vs2022``. Default is ``vs2019``.

    -a, --architecture NAME
        Specifies the CPU architecture to build for. Valid options are ``x86`` (32-bit Intel, available on Windows, Linux, and Android), ``x64`` (64-bit Intel, available on Windows, Linux, macOS, and Android), ``armv7`` (32-bit ARM, available on Android), and ``arm64`` (64-bit ARM, available on Android and iOS). Default is ``x64`` for Windows, Linux, and macOS; ``armv7`` for Android; and ``arm64`` for iOS.

    -c, --configuration NAME
        Specifies the build configuration. Valid options are ``debug`` and ``release``. Default is ``release``.

    -o, --operation NAME
        Specifies the CMake operation to perform. Valid options are ``generate`` (generates the build system), ``build`` (build all targets in the build system), ``install`` (runs the ``INSTALL`` target, which copies binaries to the ``bin/`` folder), ``package`` (runs the ``PACKAGE`` target, which generates the zip file and copies it to the ``dist/`` folder), ``ci_build`` (which runs ``generate``, then ``build``, then ``install``), and ``ci_package`` (which runs ``generate``, then ``package``). Default is ``generate`` followed by ``build``.

Building using CMake directly
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

The Python build scripts discussed in above section are a wrapper around CMake. You can also directly use CMake to generate the build system. This is useful if you need to specify non-default paths for CMake to search for dependencies. To do this, first create the directory in which you want to generate the build system::

    $ cd build
    $ mkdir windows-vs2019-x64

Now run CMake::

    $ cmake ../..

Alternatively, use ``cmake-gui`` if you prefer using a GUI to configure various CMake options::

    $ cmake-gui ../..

When building for Android, Steam Audio provides the following toolchain files that you can use:

======================================= ============
Toolchain File                          Platform
======================================= ============
``build/toolchain_android_armv7.cmake`` 32-bit ARM
``build/toolchain_android_armv8.cmake`` 64-bit ARM
``build/toolchain_android_x86.cmake``   32-bit Intel
``build/toolchain_android_x64.cmake``   64-bit Intel
======================================= ============

When building for iOS, Steam Audio provides the following toolchain files that you can use:

======================================= ============
Toolchain File                          Platform
======================================= ============
``build/toolchain_ios.cmake``           64-bit ARM
======================================= ============

Below are some of the CMake options you may want to configure:

==============================  ======================================================================
Option                          Description
==============================  ======================================================================
``STEAMAUDIOWWISE_BUILD_DOCS``  ``TRUE`` if you want to build documentation, ``FALSE`` otherwise.
``CMAKE_ANDROID_NDK``           Absolute path to the Android NDK.
``CMAKE_MAKE_PROGRAM``          Absolute path to the ``make`` executable in the Android NDK.
``Sphinx_EXECUTABLE_DIR``       Absolute path to the directory containing the Sphinx executable.
``DOXYGEN_EXECUTABLE``          Absolute path to the Doxygen executable.
==============================  ======================================================================


Building using the Wwise SDK directly
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

The CMake build system generated by the Steam Audio Wwise plugin source code is itself a wrapper around the Wwise SDK plugin build scripts. Instead of using ``build.py`` or CMake, you can directly use the Wwise SDK plugin build script, ``wp.py``, to build the plugin. This is useful if you need more fine-grained control or customization over which build tools and configurations are used when building the plugin.

As an example, from the ``wise`` directory of the repository:

    $ cd src
    $ $WWISEROOT/Scripts/Build/Plugins/wp.py premake Authoring
    $ $WWISEROOT/Scripts/Build/Plugins/wp.py build -x x64 -t vc170 -c Release

Here, ``$WWISEROOT`` is the directory in which Wwise has been installed. In the above sequence of commands, the second command generates the Visual Studio projects for building the part of the plugin that integrates with the Wwise authoring app. The third command runs the Visual Studio 2022 (``vc170``) C++ compiler to build the plugin for 64-bit Windows, in the Release configuration.

For more information on how to use ``wp.py`` to build Wwise plugins, refer to the Wwise SDK documentation.


Generating the zip file
-----------------------

As an optional step, you can package the plugin, including documentation, into a zip file. To do this, run the following from a command prompt in the ``wwise`` directory of the repository::

    $ cd build
    $ python build.py -o install
    $ python build.py -o package

This will place the generated zip file in ``dist/steamaudio_wwise.zip``.
