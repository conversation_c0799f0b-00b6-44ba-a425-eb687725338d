use crate::error::{to_option_error, SteamAudioError};
use crate::version::SteamAudioVersion;

/// A context object, which controls low-level operations of Steam Audio.
///
/// Typically, a context is specified once during the execution of the client program, before calling any other API functions.
#[derive(Debug)]
pub struct Context(pub(crate) audionimbus_sys::IPLContext);

impl Context {
    pub fn try_new(settings: &ContextSettings) -> Result<Self, SteamAudioError> {
        let mut context = Self(std::ptr::null_mut());

        let status = unsafe {
            audionimbus_sys::iplContextCreate(
                &mut audionimbus_sys::IPLContextSettings::from(settings),
                context.raw_ptr_mut(),
            )
        };

        if let Some(error) = to_option_error(status) {
            return Err(error);
        }

        Ok(context)
    }

    pub fn raw_ptr(&self) -> audionimbus_sys::IPLContext {
        self.0
    }

    pub fn raw_ptr_mut(&mut self) -> &mut audionimbus_sys::IPLContext {
        &mut self.0
    }
}

impl Clone for Context {
    fn clone(&self) -> Self {
        unsafe {
            audionimbus_sys::iplContextRetain(self.0);
        }
        Self(self.0)
    }
}

impl Drop for Context {
    fn drop(&mut self) {
        unsafe { audionimbus_sys::iplContextRelease(&mut self.0) }
    }
}

unsafe impl Send for Context {}
unsafe impl Sync for Context {}

/// Settings used to create a [`Context`].
pub struct ContextSettings {
    /// The API version.
    ///
    /// Context creation will fail if `phonon.dll` does not implement a compatible version of the API.
    /// Typically, this should be set to [`SteamAudioVersion::default()`].
    pub version: SteamAudioVersion,

    /// If `Some`, Steam Audio will call this function to record log messages generated by certain operations.
    pub log_callback: Option<
        unsafe extern "C" fn(
            level: audionimbus_sys::IPLLogLevel,
            message: *const std::os::raw::c_char,
        ),
    >,

    /// If `Some`, Steam Audio will call this function whenever it needs to allocate memory.
    pub allocate_callback:
        Option<unsafe extern "C" fn(size: usize, alignment: usize) -> *mut std::ffi::c_void>,

    /// If `Some`, Steam Audio will call this function whenever it needs to free memory.
    pub free_callback: Option<unsafe extern "C" fn(memory_block: *mut std::ffi::c_void)>,

    /// The maximum SIMD instruction set level that Steam Audio should use.
    ///
    /// Steam Audio automatically chooses the best instruction set to use based on the user’s CPU, but you can prevent it from using certain newer instruction sets using this parameter.
    /// For example, with some workloads, AVX512 instructions consume enough power that the CPU clock speed will be throttled, resulting in lower performance than expected.
    /// If you observe this in your application, set this parameter to IPL_SIMDLEVEL_AVX2 or lower.
    pub simd_level: SimdLevel,

    /// Additional flags for modifying the behavior of the created context.
    pub flags: ContextFlags,
}

impl Default for ContextSettings {
    fn default() -> Self {
        Self {
            version: SteamAudioVersion::default(),
            log_callback: None,
            allocate_callback: None,
            free_callback: None,
            simd_level: SimdLevel::default(),
            flags: ContextFlags::empty(),
        }
    }
}

impl From<&ContextSettings> for audionimbus_sys::IPLContextSettings {
    fn from(settings: &ContextSettings) -> Self {
        Self {
            version: settings.version.into(),
            logCallback: settings.log_callback,
            allocateCallback: settings.allocate_callback,
            freeCallback: settings.free_callback,
            simdLevel: settings.simd_level.into(),
            flags: settings.flags.into(),
        }
    }
}

/// SIMD instruction sets that Steam Audio can attempt to use.
#[derive(Debug, Copy, Clone)]
pub enum SimdLevel {
    /// Intel Streaming SIMD Extensions 2.
    /// Up to 4 simultaneous floating-point operations.
    SSE2 = 0,

    /// Intel Streaming SIMD Extensions 4.2 or older.
    /// Up to 4 simultaneous floating-point operations.
    SSE4 = 1,

    /// Intel Advanced Vector Extensions or older.
    /// Up to 8 simultaneous floating-point operations.
    AVX = 2,

    /// Intel Advanced Vector Extensions 2 or older.
    /// Up to 8 simultaneous floating-point operations.
    AVX2 = 3,

    /// Intel Advanced Vector Extensions 512 or older.
    /// Up to 16 simultaneous floating-point operations.
    AVX512 = 4,
}

impl Default for SimdLevel {
    fn default() -> Self {
        Self::AVX512
    }
}

impl From<SimdLevel> for audionimbus_sys::IPLSIMDLevel {
    fn from(simd_level: SimdLevel) -> Self {
        match simd_level {
            SimdLevel::SSE2 => Self::IPL_SIMDLEVEL_SSE2,
            SimdLevel::SSE4 => Self::IPL_SIMDLEVEL_SSE4,
            SimdLevel::AVX => Self::IPL_SIMDLEVEL_AVX,
            SimdLevel::AVX2 => Self::IPL_SIMDLEVEL_AVX2,
            SimdLevel::AVX512 => Self::IPL_SIMDLEVEL_AVX512,
        }
    }
}

bitflags::bitflags! {
    /// Additional flags for modifying the behavior of a Steam Audio context.
    #[derive(Debug, Copy, Clone)]
    pub struct ContextFlags: u32 {
        /// All API functions perform extra validation checks.
        /// NOTE: This imposes a significant performance penalty.
        const VALIDATION = 1 << 0;

        /// Force this enum to be 32 bits in size.
        const FORCE_32BIT = 1 << 1;
    }
}

impl From<ContextFlags> for audionimbus_sys::IPLContextFlags {
    fn from(context_flags: ContextFlags) -> Self {
        Self(context_flags.bits() as _)
    }
}
