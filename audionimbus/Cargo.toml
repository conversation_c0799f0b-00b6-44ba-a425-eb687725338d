[package]
name = "audionimbus"
description = "A safe wrapper around Steam Audio that provides spatial audio capabilities with realistic occlusion, reverb, and HRTF effects, accounting for physical attributes and scene geometry."
version = "0.7.1"
edition = "2021"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
license = "MIT OR Apache-2.0"
keywords = ["gamedev", "game", "audio", "engine", "steam"]
categories = ["game-development", "game-engines", "multimedia::audio", "simulation", "api-bindings"]
repository = "https://github.com/MaxenceMaire/audionimbus"
readme = "README.md"

[dependencies]
audionimbus-sys = { version = "4.6.2-fmodwwise.2", path = "../audionimbus-sys" }
bitflags = "2.8.0"

[features]
fmod = ["audionimbus-sys/fmod"]
wwise = ["audionimbus-sys/wwise"]

[package.metadata.docs.rs]
features = ["fmod"]
rustdoc-args = ["--cfg", "docsrs"]
